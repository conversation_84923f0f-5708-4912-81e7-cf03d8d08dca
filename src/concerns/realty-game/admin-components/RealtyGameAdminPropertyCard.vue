<template>
  <q-card
    :id="`property-${property.uuid}`"
    :class="['property-card q-mb-md', { 'property-hidden': !property.visible }]"
  >
    <q-card-section>
      <div class="row items-start justify-between">
        <div class="col-8">
          <div class="property-header q-mb-sm">
            <q-badge
              :color="property.visible ? 'positive' : 'negative'"
              :label="property.visible ? 'Visible' : 'Hidden'"
              class="q-mr-sm"
            />
            <span class="text-caption text-grey-6">
              Property {{ index + 1 }}
            </span>
          </div>

          <div>{{ localGlTitleAtr }} ({{ localTitle }})</div>
          <div>sale_listing.visible: {{ property.visible }}</div>
          <div>rgl.visible_in_game: {{ realtyGameListing?.visible_in_game }}</div>
          <!-- Editable Title -->
          <!-- <div class="property-title q-mb-sm">
            <q-input
              v-model="localTitle"
              label="Property Title"
              outlined
              dense
              @blur="onTitleBlur"
              :loading="property._updating"
            />
          </div> -->

          <!-- GL Attributes Section -->
          <q-expansion-item
            icon="edit"
            label="Game Listing Attributes (GL)"
            header-class="text-secondary"
            class="q-mb-sm"
            expand-separator
          >
            <q-card>
              <q-card-section>
                <div class="text-caption text-grey-6 q-mb-md">
                  These attributes override the default property data when
                  displayed in games
                </div>
                <div class="row q-gutter-md">
                  <div class="col-12 col-md-5">
                    <q-input
                      v-model="localGlTitleAtr"
                      label="GL Title"
                      outlined
                      dense
                      @blur="onGlTitleAtrBlur"
                      :loading="realtyGameListing?._updating"
                      hint="Override title for game display"
                    />
                  </div>
                  <div class="col-12 col-md-5">
                    <q-input
                      v-model="localGlVicinityAtr"
                      label="GL Vicinity"
                      outlined
                      dense
                      @blur="onGlVicinityAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Location area for game display"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      v-model="localGlDescriptionAtr"
                      label="GL Description"
                      outlined
                      type="textarea"
                      rows="3"
                      @blur="onGlDescriptionAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Custom description for game"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      v-model="localGlImageUrlAtr"
                      label="GL Image URL"
                      outlined
                      dense
                      @blur="onGlImageUrlAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Custom image URL for game"
                    />
                  </div>
                  <div class="col-12 col-md-3">
                    <q-input
                      v-model="localGlCountryCodeAtr"
                      label="GL Country Code"
                      outlined
                      dense
                      @blur="onGlCountryCodeAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Country code (e.g., GB, US)"
                      maxlength="2"
                    />
                  </div>
                  <div class="col-12 col-md-4">
                    <q-input
                      v-model="localGlLatitudeAtr"
                      label="GL Latitude"
                      outlined
                      dense
                      type="number"
                      step="any"
                      @blur="onGlLatitudeAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Latitude coordinate"
                    />
                  </div>
                  <div class="col-12 col-md-4">
                    <q-input
                      v-model="localGlLongitudeAtr"
                      label="GL Longitude"
                      outlined
                      dense
                      type="number"
                      step="any"
                      @blur="onGlLongitudeAtrBlur"
                      :loading="realtyGameListing._updating"
                      hint="Longitude coordinate"
                    />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>

          <!-- Key Summary Information -->
          <div class="property-details text-caption text-grey-6 q-mb-sm">
            <div>{{ property.formatted_display_price }}</div>
            <div>{{ property.street_address }}, {{ property.city }}</div>
            <div>
              {{ property.count_bedrooms }} bed,
              {{ property.count_bathrooms }} bath
            </div>
          </div>

          <!-- First Property Image (if available) -->
          <div v-if="property.sale_listing_pics?.length > 0" class="q-mb-sm">
            <div class="first-image-container">
              <img
                :src="
                  property.sale_listing_pics[0].image_details?.small_fit?.url ||
                  property.sale_listing_pics[0].photo_slug
                "
                :alt="property.sale_listing_pics[0].photo_title"
                class="first-property-image"
              />
            </div>
          </div>

          <!-- Expandable Details Section -->
          <q-expansion-item
            icon="info"
            label="View Details"
            header-class="text-primary"
            expand-separator
          >
            <q-card>
              <q-card-section>
                <div
                  v-if="property.listing_display_url"
                  class="text-subtitle1 text-weight-medium q-mb-sm"
                >
                  <a :href="property.listing_display_url">{{
                    property.listing_display_url
                  }}</a>
                </div>

                <div v-if="realtyGameListingUuid" class="q-mb-sm">
                  <router-link
                    :to="{
                      name: 'rPriceGameProperty',
                      params: { listingInGameUuid: realtyGameListingUuid },
                    }"
                    class="full-width text-green"
                  >
                    Regular game prop:
                    {{
                      $router.resolve({
                        name: 'rPriceGameProperty',
                        params: {
                          listingInGameUuid: realtyGameListingUuid,
                        },
                      }).href
                    }}
                  </router-link>
                </div>

                <div v-if="realtyGameListingUuid" class="q-mb-sm">
                  <router-link
                    :to="{
                      name: 'rPriceGamePropertySuperbee',
                      params: {
                        listingInGameUuid: realtyGameListingUuid,
                      },
                    }"
                    class="full-width text-green"
                  >
                    Regular game superbee:
                    {{
                      $router.resolve({
                        name: 'rPriceGamePropertySuperbee',
                        params: {
                          listingInGameUuid: realtyGameListingUuid,
                        },
                      }).href
                    }}
                  </router-link>
                </div>

                <div v-if="realtyGameListingUuid" class="q-mb-sm">
                  <router-link
                    :to="{
                      name: 'rRoundupGameProperty',
                      params: { listingInGameUuid: realtyGameListingUuid },
                    }"
                    class="full-width text-green"
                  >
                    Round up prop:
                    {{
                      $router.resolve({
                        name: 'rRoundupGameProperty',
                        params: {
                          listingInGameUuid: realtyGameListingUuid,
                        },
                      }).href
                    }}
                  </router-link>
                </div>

                <div v-if="realtyGameListingUuid" class="q-mb-sm">
                  <router-link
                    :to="{
                      name: 'rRoundupGamePropertySuperbee',
                      params: { listingInGameUuid: realtyGameListingUuid },
                    }"
                    class="full-width text-green"
                  >
                    Round up superbee:
                    {{
                      $router.resolve({
                        name: 'rRoundupGamePropertySuperbee',
                        params: {
                          listingInGameUuid: realtyGameListingUuid,
                        },
                      }).href
                    }}
                  </router-link>
                </div>

                <div class="q-mb-sm">
                  <router-link
                    :to="{
                      name: 'rOneOffGameProperty',
                      params: { propertyUuid: property.uuid },
                    }"
                    class="full-width text-green"
                  >
                    One off prop:
                    {{
                      $router.resolve({
                        name: 'rOneOffGameProperty',
                        params: {
                          propertyUuid: property.uuid,
                        },
                      }).href
                    }}
                  </router-link>
                </div>

                <!-- All Property Images -->
                <div v-if="property.sale_listing_pics?.length > 0">
                  <div class="images-header q-mb-sm">
                    <h6 class="q-my-none">
                      Property Images ({{ property.sale_listing_pics.length }})
                    </h6>
                    <p class="text-caption text-grey-6 q-mb-none">
                      Toggle individual image visibility
                    </p>
                  </div>

                  <div class="images-grid">
                    <div
                      v-for="(image, imgIndex) in property.sale_listing_pics"
                      :key="image.uuid"
                      class="image-item"
                      :class="{ 'image-hidden': image.flag_is_hidden }"
                    >
                      <div class="image-container">
                        <img
                          :src="
                            image.image_details?.small_fit?.url ||
                            image.photo_slug
                          "
                          :alt="image.photo_title"
                          class="property-image"
                        />
                        <div class="image-overlay">
                          <q-toggle
                            :model-value="!image.flag_is_hidden"
                            @update:model-value="
                              onImageVisibilityToggle(image, !$event)
                            "
                            color="white"
                            size="sm"
                            :loading="image._updating"
                          />
                        </div>
                      </div>
                      <div class="image-info q-mt-xs">
                        <div class="text-caption font-weight-medium">
                          {{ image.photo_title || `Image ${imgIndex + 1}` }}
                        </div>
                        <div class="text-caption text-grey-6">
                          {{ image.flag_is_hidden ? 'Hidden' : 'Visible' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </div>

        <div class="col-4 text-right">
          <!-- Property Visibility Toggle -->
          <q-toggle
            v-model="localVisible"
            color="positive"
            size="lg"
            @update:model-value="onVisibilityToggle"
            :loading="property._updating"
          />
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  property: { type: Object, required: true },
  realtyGameListing: { type: Object, required: true },
  index: { type: Number, required: true },
  realtyGameListingUuid: { type: String },
})
const emit = defineEmits([
  'update-title',
  'update-visibility',
  'update-image-visibility',
  'update-gl-attribute',
])

const localTitle = ref(props.property.title)
// const localVisible = ref(props.property.visible)
const localVisible = ref(props.realtyGameListing?.visible_in_game)

// Local state for GL attributes
const localGlTitleAtr = ref(props.realtyGameListing?.gl_title_atr || '')
const localGlDescriptionAtr = ref(
  props.realtyGameListing?.gl_description_atr || ''
)
const localGlImageUrlAtr = ref(props.realtyGameListing?.gl_image_url_atr || '')
const localGlVicinityAtr = ref(props.realtyGameListing?.gl_vicinity_atr || '')
const localGlCountryCodeAtr = ref(
  props.realtyGameListing?.gl_country_code_atr || ''
)
const localGlLatitudeAtr = ref(props.realtyGameListing?.gl_latitude_atr || '')
const localGlLongitudeAtr = ref(props.realtyGameListing?.gl_longitude_atr || '')

watch(
  () => props.property.title,
  (newVal) => {
    localTitle.value = newVal
  }
)
watch(
  () => props.property.visible,
  (newVal) => {
    localVisible.value = newVal
  }
)

// Watchers for GL attributes
watch(
  () => props.realtyGameListing.gl_title_atr,
  (newVal) => {
    localGlTitleAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_description_atr,
  (newVal) => {
    localGlDescriptionAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_image_url_atr,
  (newVal) => {
    localGlImageUrlAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_vicinity_atr,
  (newVal) => {
    localGlVicinityAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_country_code_atr,
  (newVal) => {
    localGlCountryCodeAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_latitude_atr,
  (newVal) => {
    localGlLatitudeAtr.value = newVal || ''
  }
)
watch(
  () => props.realtyGameListing.gl_longitude_atr,
  (newVal) => {
    localGlLongitudeAtr.value = newVal || ''
  }
)

function onTitleBlur() {
  if (localTitle.value !== props.property.title) {
    emit('update-title', localTitle.value)
  }
}
function onVisibilityToggle(val) {
  emit('update-visibility', val)
}
function onImageVisibilityToggle(image, isHidden) {
  emit('update-image-visibility', image, isHidden)
}

// GL attribute blur handlers
function onGlTitleAtrBlur() {
  if (localGlTitleAtr.value !== props.realtyGameListing.gl_title_atr) {
    emit('update-gl-attribute', 'gl_title_atr', localGlTitleAtr.value)
  }
}
function onGlDescriptionAtrBlur() {
  if (
    localGlDescriptionAtr.value !== props.realtyGameListing.gl_description_atr
  ) {
    emit(
      'update-gl-attribute',
      'gl_description_atr',
      localGlDescriptionAtr.value
    )
  }
}
function onGlImageUrlAtrBlur() {
  if (localGlImageUrlAtr.value !== props.realtyGameListing.gl_image_url_atr) {
    emit('update-gl-attribute', 'gl_image_url_atr', localGlImageUrlAtr.value)
  }
}
function onGlVicinityAtrBlur() {
  if (localGlVicinityAtr.value !== props.realtyGameListing.gl_vicinity_atr) {
    emit('update-gl-attribute', 'gl_vicinity_atr', localGlVicinityAtr.value)
  }
}
function onGlCountryCodeAtrBlur() {
  if (
    localGlCountryCodeAtr.value !== props.realtyGameListing.gl_country_code_atr
  ) {
    emit(
      'update-gl-attribute',
      'gl_country_code_atr',
      localGlCountryCodeAtr.value
    )
  }
}
function onGlLatitudeAtrBlur() {
  if (localGlLatitudeAtr.value !== props.realtyGameListing.gl_latitude_atr) {
    emit('update-gl-attribute', 'gl_latitude_atr', localGlLatitudeAtr.value)
  }
}
function onGlLongitudeAtrBlur() {
  if (localGlLongitudeAtr.value !== props.realtyGameListing.gl_longitude_atr) {
    emit('update-gl-attribute', 'gl_longitude_atr', localGlLongitudeAtr.value)
  }
}
</script>

<style scoped>
.property-card {
  transition: opacity 0.3s ease;
}
.property-hidden {
  opacity: 0.6;
}
.property-header {
  display: flex;
  align-items: center;
}
.first-image-container {
  border-radius: 8px;
  overflow: hidden;
  max-width: 200px;
}
.first-property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}
.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}
.image-item {
  transition: opacity 0.3s ease;
}
.image-hidden {
  opacity: 0.5;
}
.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}
.property-image {
  width: 100%;
  height: 120px;
  object-fit: cover;
  display: block;
}
.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px;
}
.image-info {
  text-align: center;
}
</style>
